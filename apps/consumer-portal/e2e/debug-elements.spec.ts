import { test, expect } from '@playwright/test';
import {
  loginToPortal,
  navigateToPage,
  waitForPageStable,
  ELEMENTS,
  TIMEOUTS
} from './test-utils';

test.describe('Debug Element Selectors', () => {
  test.beforeEach(async ({ page }) => {
    await loginToPortal(page);
    await navigateToPage(page, 'home');
    await waitForPageStable(page);
  });

  test('should debug dashboard elements', async ({ page }) => {
    console.log('=== DEBUGGING DASHBOARD ELEMENTS ===');
    
    // Test Current Balance
    console.log('Testing Current Balance selectors...');
    const currentBalanceSelectors = [
      'text="Current Balance"',
      'paragraph:has-text("Current Balance")',
      '*:has-text("Current Balance")'
    ];
    
    for (const selector of currentBalanceSelectors) {
      const elements = page.locator(selector);
      const count = await elements.count();
      console.log(`Selector "${selector}": ${count} elements found`);
      
      if (count > 0) {
        const isVisible = await elements.first().isVisible();
        console.log(`  First element visible: ${isVisible}`);
        
        if (isVisible) {
          const text = await elements.first().textContent();
          console.log(`  Text content: "${text}"`);
        }
      }
    }
    
    // Test Available Credit
    console.log('\nTesting Available Credit selectors...');
    const availableCreditSelectors = [
      'text="Available Credit"',
      'paragraph:has-text("Available Credit")',
      'text=/Available Credit/',
      '*:has-text("Available Credit")'
    ];
    
    for (const selector of availableCreditSelectors) {
      const elements = page.locator(selector);
      const count = await elements.count();
      console.log(`Selector "${selector}": ${count} elements found`);
      
      if (count > 0) {
        const isVisible = await elements.first().isVisible();
        console.log(`  First element visible: ${isVisible}`);
        
        if (isVisible) {
          const text = await elements.first().textContent();
          console.log(`  Text content: "${text}"`);
        }
      }
    }
    
    // Test Rewards Balance
    console.log('\nTesting Rewards Balance selectors...');
    const rewardsBalanceSelectors = [
      'text="Rewards Balance"',
      'heading:has-text("Rewards Balance")',
      'h6:has-text("Rewards Balance")',
      '*:has-text("Rewards Balance")'
    ];
    
    for (const selector of rewardsBalanceSelectors) {
      const elements = page.locator(selector);
      const count = await elements.count();
      console.log(`Selector "${selector}": ${count} elements found`);
      
      if (count > 0) {
        const isVisible = await elements.first().isVisible();
        console.log(`  First element visible: ${isVisible}`);
        
        if (isVisible) {
          const text = await elements.first().textContent();
          console.log(`  Text content: "${text}"`);
        }
      }
    }
    
    // Test Settings Button
    console.log('\nTesting Settings Button selectors...');
    const settingsSelectors = [
      'button:has-text("Settings")',
      'text="Settings"',
      'button[aria-label*="Settings"]',
      '*:has-text("Settings")'
    ];
    
    for (const selector of settingsSelectors) {
      const elements = page.locator(selector);
      const count = await elements.count();
      console.log(`Selector "${selector}": ${count} elements found`);
      
      if (count > 0) {
        const isVisible = await elements.first().isVisible();
        console.log(`  First element visible: ${isVisible}`);
        
        if (isVisible) {
          const text = await elements.first().textContent();
          console.log(`  Text content: "${text}"`);
          
          // Try to get more info about the element
          const tagName = await elements.first().evaluate(el => el.tagName);
          const className = await elements.first().getAttribute('class');
          console.log(`  Tag: ${tagName}, Class: ${className}`);
        }
      }
    }
    
    console.log('\n=== END DEBUG ===');
  });

  test('should test actual element interactions', async ({ page }) => {
    console.log('=== TESTING ACTUAL INTERACTIONS ===');
    
    // Try to find and click Current Balance
    try {
      const currentBalance = page.locator('paragraph:has-text("Current Balance")').first();
      const isVisible = await currentBalance.isVisible({ timeout: 3000 });
      console.log(`Current Balance visible: ${isVisible}`);
      
      if (isVisible) {
        console.log('✓ Current Balance found and visible');
      }
    } catch (error) {
      console.log(`Current Balance error: ${error}`);
    }
    
    // Try to find and click Settings button
    try {
      const settingsButton = page.locator('button:has-text("Settings")').first();
      const isVisible = await settingsButton.isVisible({ timeout: 3000 });
      console.log(`Settings button visible: ${isVisible}`);
      
      if (isVisible) {
        console.log('✓ Settings button found and visible');
        await settingsButton.click();
        console.log('✓ Settings button clicked successfully');
      } else {
        // Try alternative selectors
        const altSettings = page.locator('text="Settings"').first();
        const altVisible = await altSettings.isVisible({ timeout: 3000 });
        console.log(`Alternative Settings selector visible: ${altVisible}`);
      }
    } catch (error) {
      console.log(`Settings button error: ${error}`);
    }
    
    console.log('\n=== END INTERACTION TEST ===');
  });
});
