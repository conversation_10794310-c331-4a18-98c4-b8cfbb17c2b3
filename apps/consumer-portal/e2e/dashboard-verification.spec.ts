import { test, expect } from '@playwright/test';
import {
  loginToPortal,
  VIEWPORTS,
  ELEMENTS,
  switchToTab,
  navigateToPage,
  verifyNavigation,
  verifyDashboardContent,
  downloadAndVerifyPdf,
  waitForPageStable,
  verifyTextContent
} from './test-utils';

test.describe('Dashboard UI Verification', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.desktop);
    await loginToPortal(page);
  });

  test('should display main navigation elements correctly', async ({ page }) => {
    await page.goto('/');
    await waitForPageStable(page);
    await verifyNavigation(page);
  });

  test('should display home page tabs and allow switching', async ({ page }) => {
    await page.goto('/');
    await waitForPageStable(page);

    // Verify both tabs are visible
    const recentActivityTab = page.locator(ELEMENTS.tabs.recentActivity).first();
    const statementsTab = page.locator(ELEMENTS.tabs.statements).first();
    await expect(recentActivityTab).toBeVisible();
    await expect(statementsTab).toBeVisible();

    // Test tab switching
    await switchToTab(page, 'statements');
    await expect(statementsTab).toBeVisible();

    await switchToTab(page, 'recentActivity');
    await expect(recentActivityTab).toBeVisible();
  });

  test('should navigate to Payments page and display content', async ({ page }) => {
    await navigateToPage(page, 'payments');
    await expect(page.locator('body')).toBeVisible();
  });

  test('should navigate to Rewards page and display content', async ({ page }) => {
    await navigateToPage(page, 'rewards');

    // Verify we're on the rewards page
    await expect(page).toHaveURL(/\/rewards$/);

    // Verify page content is loaded and visible
    await expect(page.locator('body')).toBeVisible();

    // Wait for page content to stabilize using enhanced method
    await waitForPageStable(page);

    // Verify rewards-specific content is present using enhanced verification
    await verifyTextContent(page, ['Rewards'], 5000);
  });

  test('should verify dashboard content structure and key elements', async ({ page }) => {
    await page.goto('/');
    await waitForPageStable(page);
    await verifyDashboardContent(page);
  });

  test('should verify statements tab shows statement content', async ({ page }) => {
    await page.goto('/');
    await waitForPageStable(page);

    await switchToTab(page, 'statements');
    await expect(page.locator('body')).toBeVisible();

    // Look for statement-related content using enhanced verification
    await verifyTextContent(page, ['2024', '2023'], 5000); // Should contain recent years
  });

  test('should verify PDF functionality', async ({ page }) => {
    await page.goto('/');
    await waitForPageStable(page);

    await switchToTab(page, 'statements');

    // Look for statement PDF links
    const statementPdfSelector = 'a[href*=".pdf"]';
    const hasStatementPdfs = await downloadAndVerifyPdf(page, statementPdfSelector);

    if (!hasStatementPdfs) {
      console.log('No statement PDF links found - test account may not have statements yet');
    }
  });



});
