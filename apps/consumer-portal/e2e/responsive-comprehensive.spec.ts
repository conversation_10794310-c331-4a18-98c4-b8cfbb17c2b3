import { test, expect } from '@playwright/test';
import {
  loginToPortal,
  navigateToPage,
  switchToTab,
  ensureNavigationAccessible,
  isMobileNavigation,
  accessMobileNavigation,
  waitForPageStable,
  safeClick,
  verifyDashboardContent,
  ELEMENTS,
  TIMEOUTS,
  VIEWPORTS
} from './test-utils';

test.describe('Cross-Platform Responsive Tests', () => {
  test.beforeEach(async ({ page }) => {
    await loginToPortal(page);
  });

  test.describe('Desktop vs Mobile Navigation', () => {
    test('should display desktop navigation on large screens', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.desktop);
      await navigateToPage(page, 'home');
      
      console.log('Testing desktop navigation...');
      
      // Verify navigation items are visible without hamburger menu
      const homeNav = page.locator(ELEMENTS.navigation.home).first();
      const paymentsNav = page.locator(ELEMENTS.navigation.payments).first();
      const rewardsNav = page.locator(ELEMENTS.navigation.rewards).first();
      
      await expect(homeNav).toBeVisible({ timeout: TIMEOUTS.medium });
      await expect(paymentsNav).toBeVisible({ timeout: TIMEOUTS.medium });
      await expect(rewardsNav).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Verify hamburger menu is NOT visible on desktop
      const isMobile = await isMobileNavigation(page);
      expect(isMobile).toBe(false);
      
      console.log('✓ Desktop navigation layout verified');
    });

    test('should display mobile navigation on small screens', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await navigateToPage(page, 'home');
      
      console.log('Testing mobile navigation...');
      
      // Check if mobile navigation is detected
      const isMobile = await isMobileNavigation(page);
      
      if (isMobile) {
        console.log('Mobile navigation detected');
        
        // Verify hamburger menu is visible
        const hamburgerMenu = page.locator(ELEMENTS.mobile.hamburgerMenu).first();
        await expect(hamburgerMenu).toBeVisible({ timeout: TIMEOUTS.medium });
        
        // Test opening mobile navigation
        await accessMobileNavigation(page);
        
        // Verify navigation items become visible after opening menu
        const homeNav = page.locator(ELEMENTS.navigation.home).first();
        await expect(homeNav).toBeVisible({ timeout: TIMEOUTS.medium });
        
        console.log('✓ Mobile navigation layout verified');
      } else {
        console.log('ℹ Desktop navigation detected on mobile viewport (responsive design choice)');
      }
    });

    test('should handle navigation transitions between viewports', async ({ page }) => {
      // Start with desktop
      await page.setViewportSize(VIEWPORTS.desktop);
      await navigateToPage(page, 'home');
      
      // Verify desktop navigation works
      await navigateToPage(page, 'payments');
      await expect(page).toHaveURL(/\/payments$/);
      
      // Switch to mobile viewport
      await page.setViewportSize(VIEWPORTS.mobile);
      await waitForPageStable(page);
      
      // Ensure navigation is still accessible
      await ensureNavigationAccessible(page);
      
      // Test navigation on mobile
      await navigateToPage(page, 'rewards');
      await expect(page).toHaveURL(/\/rewards$/);
      
      console.log('✓ Navigation transitions between viewports verified');
    });
  });

  test.describe('Touch vs Click Interactions', () => {
    test('should handle click interactions on desktop', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.desktop);
      await navigateToPage(page, 'home');
      
      console.log('Testing desktop click interactions...');
      
      // Test clicking navigation links
      const paymentsNav = page.locator(ELEMENTS.navigation.payments).first();
      await paymentsNav.click();
      await expect(page).toHaveURL(/\/payments$/);
      
      // Test clicking action buttons
      await navigateToPage(page, 'home');
      const makePaymentButton = page.locator(ELEMENTS.dashboard.makePaymentButton).first();
      if (await makePaymentButton.isVisible({ timeout: 3000 })) {
        await makePaymentButton.click();
        await expect(page).toHaveURL(/\/payments$/);
      }
      
      console.log('✓ Desktop click interactions verified');
    });

    test('should handle touch interactions on mobile', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await navigateToPage(page, 'home');
      
      console.log('Testing mobile touch interactions...');
      
      // Test touch navigation
      await ensureNavigationAccessible(page);
      const rewardsNav = page.locator(ELEMENTS.navigation.rewards).first();
      await rewardsNav.tap();
      await expect(page).toHaveURL(/\/rewards$/);
      
      // Test touch on action buttons
      await navigateToPage(page, 'home');
      const redeemButton = page.locator(ELEMENTS.dashboard.redeemButton).first();
      if (await redeemButton.isVisible({ timeout: 3000 })) {
        await redeemButton.tap();
        await expect(page).toHaveURL(/\/rewards$/);
      }
      
      console.log('✓ Mobile touch interactions verified');
    });

    test('should handle hover states on desktop', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.desktop);
      await navigateToPage(page, 'home');
      
      // Test hover on navigation items
      const homeNav = page.locator(ELEMENTS.navigation.home).first();
      await homeNav.hover();
      
      // Test hover on buttons
      const makePaymentButton = page.locator(ELEMENTS.dashboard.makePaymentButton).first();
      if (await makePaymentButton.isVisible({ timeout: 3000 })) {
        await makePaymentButton.hover();
      }
      
      console.log('✓ Desktop hover interactions tested');
    });
  });

  test.describe('Responsive Layout Verification', () => {
    test('should maintain content readability across viewports', async ({ page }) => {
      const viewports = [
        { name: 'Desktop', size: VIEWPORTS.desktop },
        { name: 'Mobile', size: VIEWPORTS.mobile },
        { name: 'Tablet', size: { width: 768, height: 1024 } },
        { name: 'Small Mobile', size: { width: 375, height: 667 } }
      ];
      
      for (const viewport of viewports) {
        console.log(`Testing ${viewport.name} layout...`);
        
        await page.setViewportSize(viewport.size);
        await navigateToPage(page, 'home');
        await waitForPageStable(page);
        
        // Verify essential content is visible
        await verifyDashboardContent(page);
        
        // Verify text is readable (not too small)
        const textElements = page.locator('h1, h2, h3, h4, h5, h6, p, span, div').filter({
          hasText: /Current Balance|Available Credit|Rewards Balance/
        });
        
        const textCount = await textElements.count();
        if (textCount > 0) {
          for (let i = 0; i < Math.min(textCount, 3); i++) {
            const element = textElements.nth(i);
            const fontSize = await element.evaluate(el => {
              return window.getComputedStyle(el).fontSize;
            });
            
            const fontSizeNum = parseInt(fontSize);
            expect(fontSizeNum).toBeGreaterThanOrEqual(12); // Minimum readable font size
          }
        }
        
        console.log(`✓ ${viewport.name} layout verified`);
      }
    });

    test('should handle content overflow properly', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await navigateToPage(page, 'home');
      
      // Check for horizontal scrollbars (should not exist)
      const bodyOverflow = await page.evaluate(() => {
        return window.getComputedStyle(document.body).overflowX;
      });
      
      expect(bodyOverflow).not.toBe('scroll');
      
      // Verify content fits within viewport
      const viewportWidth = page.viewportSize()?.width || 0;
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth);
      
      expect(bodyWidth).toBeLessThanOrEqual(viewportWidth + 20); // Allow small tolerance
      
      console.log('✓ Content overflow handling verified');
    });

    test('should maintain proper spacing and alignment', async ({ page }) => {
      const viewports = [VIEWPORTS.desktop, VIEWPORTS.mobile];
      
      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await navigateToPage(page, 'home');
        await waitForPageStable(page);
        
        // Check that elements are properly spaced
        const balanceElements = page.locator('text="Current Balance", text="Available Credit", text="Rewards Balance"');
        const elementCount = await balanceElements.count();
        
        for (let i = 0; i < elementCount; i++) {
          const element = balanceElements.nth(i);
          const boundingBox = await element.boundingBox();
          
          if (boundingBox) {
            // Verify element is within viewport
            expect(boundingBox.x).toBeGreaterThanOrEqual(0);
            expect(boundingBox.y).toBeGreaterThanOrEqual(0);
            expect(boundingBox.x + boundingBox.width).toBeLessThanOrEqual(viewport.width);
          }
        }
        
        console.log(`✓ Spacing and alignment verified for ${viewport.width}px width`);
      }
    });
  });

  test.describe('Tab Functionality Across Devices', () => {
    test('should maintain tab functionality on desktop', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.desktop);
      await navigateToPage(page, 'home');
      
      // Test tab switching
      await switchToTab(page, 'statements');
      const statementsTab = page.locator(ELEMENTS.tabs.statements).first();
      await expect(statementsTab).toHaveAttribute('aria-selected', 'true');
      
      await switchToTab(page, 'recentActivity');
      const recentActivityTab = page.locator(ELEMENTS.tabs.recentActivity).first();
      await expect(recentActivityTab).toHaveAttribute('aria-selected', 'true');
      
      console.log('✓ Desktop tab functionality verified');
    });

    test('should maintain tab functionality on mobile', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await navigateToPage(page, 'home');
      
      // Test tab switching with touch
      const statementsTab = page.locator(ELEMENTS.tabs.statements).first();
      await statementsTab.tap();
      await expect(statementsTab).toHaveAttribute('aria-selected', 'true');
      
      const recentActivityTab = page.locator(ELEMENTS.tabs.recentActivity).first();
      await recentActivityTab.tap();
      await expect(recentActivityTab).toHaveAttribute('aria-selected', 'true');
      
      console.log('✓ Mobile tab functionality verified');
    });

    test('should handle tab content responsively', async ({ page }) => {
      const viewports = [VIEWPORTS.desktop, VIEWPORTS.mobile];
      
      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await navigateToPage(page, 'home');
        
        // Switch to statements tab
        await switchToTab(page, 'statements');
        
        // Verify tab content is visible and properly formatted
        const tabContent = page.locator('[role="tabpanel"]');
        if (await tabContent.isVisible({ timeout: 3000 })) {
          const boundingBox = await tabContent.boundingBox();
          if (boundingBox) {
            expect(boundingBox.width).toBeGreaterThan(0);
            expect(boundingBox.height).toBeGreaterThan(0);
          }
        }
        
        console.log(`✓ Tab content responsive for ${viewport.width}px width`);
      }
    });
  });

  test.describe('Performance Across Devices', () => {
    test('should load quickly on mobile devices', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      
      const startTime = Date.now();
      await navigateToPage(page, 'home');
      await waitForPageStable(page);
      const loadTime = Date.now() - startTime;
      
      // Verify page loads within reasonable time (8 seconds max as per requirements)
      expect(loadTime).toBeLessThan(TIMEOUTS.medium);
      
      console.log(`✓ Mobile page load time: ${loadTime}ms`);
    });

    test('should handle rapid viewport changes', async ({ page }) => {
      await navigateToPage(page, 'home');
      
      // Rapidly change viewport sizes
      const viewportSizes = [
        VIEWPORTS.desktop,
        VIEWPORTS.mobile,
        { width: 768, height: 1024 },
        VIEWPORTS.desktop
      ];
      
      for (const size of viewportSizes) {
        await page.setViewportSize(size);
        await page.waitForTimeout(200); // Brief pause
        
        // Verify page is still functional
        await expect(page.locator(ELEMENTS.dashboard.welcomeBanner)).toBeVisible({ timeout: TIMEOUTS.short });
      }
      
      console.log('✓ Rapid viewport changes handled correctly');
    });

    test('should maintain functionality during orientation changes', async ({ page }) => {
      // Simulate mobile landscape and portrait
      const orientations = [
        { width: 2868, height: 1320 }, // Mobile landscape (iPhone 16 Pro Max)
        { width: 1320, height: 2868 }  // Mobile portrait
      ];
      
      for (const orientation of orientations) {
        await page.setViewportSize(orientation);
        await navigateToPage(page, 'home');
        await waitForPageStable(page);
        
        // Verify core functionality still works
        await verifyDashboardContent(page);
        
        // Test navigation
        await ensureNavigationAccessible(page);
        await navigateToPage(page, 'payments');
        await expect(page).toHaveURL(/\/payments$/);
        
        console.log(`✓ Functionality maintained in ${orientation.width}x${orientation.height} orientation`);
      }
    });
  });

  test.describe('Accessibility Across Devices', () => {
    test('should maintain keyboard navigation on all devices', async ({ page }) => {
      const viewports = [VIEWPORTS.desktop, VIEWPORTS.mobile];
      
      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await navigateToPage(page, 'home');
        
        // Test tab navigation
        await page.keyboard.press('Tab');
        const focusedElement = page.locator(':focus');
        
        if (await focusedElement.count() > 0) {
          console.log(`✓ Keyboard navigation working on ${viewport.width}px width`);
        }
        
        // Test escape key functionality
        await page.keyboard.press('Escape');
      }
    });

    test('should maintain ARIA labels across viewports', async ({ page }) => {
      const viewports = [VIEWPORTS.desktop, VIEWPORTS.mobile];
      
      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        await navigateToPage(page, 'home');
        
        // Check for ARIA labels on interactive elements
        const ariaElements = page.locator('[aria-label], [aria-labelledby], [role]');
        const ariaCount = await ariaElements.count();
        
        expect(ariaCount).toBeGreaterThan(0);
        console.log(`✓ Found ${ariaCount} ARIA-labeled elements on ${viewport.width}px width`);
      }
    });

    test('should handle focus management responsively', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await navigateToPage(page, 'home');
      
      // Open a modal/drawer
      await safeClick(page, ELEMENTS.settings.settingsButton);
      await safeClick(page, ELEMENTS.settings.myAccountOption);
      
      // Verify focus is managed properly
      const focusedElement = page.locator(':focus');
      if (await focusedElement.count() > 0) {
        console.log('✓ Focus management working on mobile');
      }
      
      // Close modal
      await page.keyboard.press('Escape');
    });
  });
});
