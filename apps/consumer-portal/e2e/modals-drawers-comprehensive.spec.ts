import { test, expect } from '@playwright/test';
import {
  loginToPortal,
  navigateToPage,
  waitForPageStable,
  safeClick,
  ELEMENTS,
  MODAL_ELEMENTS,
  TIMEOUTS,
  VIEWPORTS
} from './test-utils';

test.describe('Modal & Drawer Comprehensive Tests', () => {
  test.beforeEach(async ({ page }) => {
    await loginToPortal(page);
    await navigateToPage(page, 'home');
    await waitForPageStable(page);
  });

  test.describe('Settings Drawer Testing', () => {
    test('should open and close My Account drawer', async ({ page }) => {
      console.log('Testing My Account drawer...');
      
      // Open settings menu
      await safeClick(page, ELEMENTS.settings.settingsButton);
      
      // Click My Account option
      await safeClick(page, ELEMENTS.settings.myAccountOption);
      
      // Verify My Account drawer opened
      await expect(page.locator(MODAL_ELEMENTS.settings.myAccountDrawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Verify close button is present and functional
      const closeButton = page.locator(MODAL_ELEMENTS.settings.myAccountDrawer.closeButton);
      await expect(closeButton).toBeVisible({ timeout: TIMEOUTS.short });
      await expect(closeButton).toBeEnabled();
      
      // Close the drawer
      await closeButton.click();
      await waitForPageStable(page);
      
      // Verify drawer is closed
      await expect(page.locator(MODAL_ELEMENTS.settings.myAccountDrawer.title)).not.toBeVisible({ timeout: TIMEOUTS.short });
      
      console.log('✓ My Account drawer functionality verified');
    });

    test('should open and close Documents drawer', async ({ page }) => {
      console.log('Testing Documents drawer...');
      
      // Open settings menu
      await safeClick(page, ELEMENTS.settings.settingsButton);
      
      // Click Documents option
      await safeClick(page, ELEMENTS.settings.documentsOption);
      
      // Verify Documents drawer opened
      await expect(page.locator(MODAL_ELEMENTS.settings.documentsDrawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Verify close button is present and functional
      const closeButton = page.locator(MODAL_ELEMENTS.settings.documentsDrawer.closeButton);
      await expect(closeButton).toBeVisible({ timeout: TIMEOUTS.short });
      await expect(closeButton).toBeEnabled();
      
      // Close the drawer
      await closeButton.click();
      await waitForPageStable(page);
      
      // Verify drawer is closed
      await expect(page.locator(MODAL_ELEMENTS.settings.documentsDrawer.title)).not.toBeVisible({ timeout: TIMEOUTS.short });
      
      console.log('✓ Documents drawer functionality verified');
    });

    test('should handle drawer overlay clicks', async ({ page }) => {
      // Open My Account drawer
      await safeClick(page, ELEMENTS.settings.settingsButton);
      await safeClick(page, ELEMENTS.settings.myAccountOption);
      
      // Verify drawer is open
      await expect(page.locator(MODAL_ELEMENTS.settings.myAccountDrawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Try clicking outside the drawer (overlay)
      const overlay = page.locator('.MuiDrawer-root .MuiBackdrop-root, .MuiModal-backdrop');
      if (await overlay.isVisible({ timeout: 2000 })) {
        await overlay.click();
        await waitForPageStable(page);
        
        // Verify drawer closed
        await expect(page.locator(MODAL_ELEMENTS.settings.myAccountDrawer.title)).not.toBeVisible({ timeout: TIMEOUTS.short });
        console.log('✓ Drawer overlay click functionality verified');
      } else {
        console.log('ℹ Drawer overlay not found or not clickable');
      }
    });
  });

  test.describe('Card Management Drawer Testing', () => {
    test('should open and close card management drawer', async ({ page }) => {
      console.log('Testing card management drawer...');
      
      // Click My Cards button
      const myCardsButton = page.locator(ELEMENTS.dashboard.myCardsButton);
      await expect(myCardsButton).toBeVisible({ timeout: TIMEOUTS.medium });
      await myCardsButton.click();
      await waitForPageStable(page);
      
      // Verify card management drawer opened
      await expect(page.locator(MODAL_ELEMENTS.cardManagement.drawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Verify close button is present
      const closeButton = page.locator(MODAL_ELEMENTS.cardManagement.drawer.closeButton);
      if (await closeButton.isVisible({ timeout: 3000 })) {
        await expect(closeButton).toBeEnabled();
        
        // Close the drawer
        await closeButton.click();
        await waitForPageStable(page);
        
        // Verify drawer is closed
        await expect(page.locator(MODAL_ELEMENTS.cardManagement.drawer.title)).not.toBeVisible({ timeout: TIMEOUTS.short });
        
        console.log('✓ Card management drawer functionality verified');
      } else {
        console.log('ℹ Card management drawer close button not found');
      }
    });

    test('should display card management options', async ({ page }) => {
      // Open card management drawer
      await safeClick(page, ELEMENTS.dashboard.myCardsButton);
      
      // Verify drawer content
      await expect(page.locator(MODAL_ELEMENTS.cardManagement.drawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Look for card management actions
      const cardActions = [
        'text="Activate"',
        'text="Cancel"',
        'text="Replace"',
        'text="Request"',
        'button:has-text("Activate")',
        'button:has-text("Cancel")',
        'button:has-text("Replace")',
        'button:has-text("Request")'
      ];
      
      let actionsFound = 0;
      for (const action of cardActions) {
        const elements = page.locator(action);
        const count = await elements.count();
        if (count > 0) {
          actionsFound += count;
          console.log(`Found ${count} elements for: ${action}`);
        }
      }
      
      if (actionsFound > 0) {
        console.log(`✓ Found ${actionsFound} card management actions`);
      } else {
        console.log('ℹ No card management actions found (may require cards)');
      }
    });

    test('should handle card action modals', async ({ page }) => {
      // Open card management drawer
      await safeClick(page, ELEMENTS.dashboard.myCardsButton);
      await expect(page.locator(MODAL_ELEMENTS.cardManagement.drawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Look for and test card action buttons
      const actionButtons = page.locator('button:has-text("Activate"), button:has-text("Cancel"), button:has-text("Replace")');
      const buttonCount = await actionButtons.count();
      
      if (buttonCount > 0) {
        const firstButton = actionButtons.first();
        const buttonText = await firstButton.textContent();
        
        await firstButton.click();
        await waitForPageStable(page);
        
        // Look for modal that should open
        const modalSelectors = [
          '[role="dialog"]',
          '.MuiDialog-root',
          '.modal',
          `text="${buttonText}"` // Modal title might match button text
        ];
        
        let modalFound = false;
        for (const selector of modalSelectors) {
          if (await page.locator(selector).isVisible({ timeout: 3000 })) {
            modalFound = true;
            console.log(`✓ Modal opened for ${buttonText} action`);
            
            // Try to close the modal
            const closeButtons = page.locator('button[aria-label*="close" i], button:has(.fa-xmark), button:has-text("Cancel")');
            const closeCount = await closeButtons.count();
            if (closeCount > 0) {
              await closeButtons.first().click();
              await waitForPageStable(page);
            }
            break;
          }
        }
        
        if (!modalFound) {
          console.log(`ℹ No modal found for ${buttonText} action`);
        }
      } else {
        console.log('ℹ No card action buttons found');
      }
    });
  });

  test.describe('Modal Opening and Closing', () => {
    test('should handle modal escape key functionality', async ({ page }) => {
      // Open a modal (using My Account drawer as example)
      await safeClick(page, ELEMENTS.settings.settingsButton);
      await safeClick(page, ELEMENTS.settings.myAccountOption);
      
      // Verify modal is open
      await expect(page.locator(MODAL_ELEMENTS.settings.myAccountDrawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Press Escape key
      await page.keyboard.press('Escape');
      await waitForPageStable(page);
      
      // Verify modal is closed
      await expect(page.locator(MODAL_ELEMENTS.settings.myAccountDrawer.title)).not.toBeVisible({ timeout: TIMEOUTS.short });
      
      console.log('✓ Modal escape key functionality verified');
    });

    test('should handle multiple modal layers', async ({ page }) => {
      // Open card management drawer
      await safeClick(page, ELEMENTS.dashboard.myCardsButton);
      await expect(page.locator(MODAL_ELEMENTS.cardManagement.drawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Try to open a card action modal on top
      const actionButtons = page.locator('button:has-text("Activate"), button:has-text("Cancel")');
      const buttonCount = await actionButtons.count();
      
      if (buttonCount > 0) {
        await actionButtons.first().click();
        await waitForPageStable(page);
        
        // Check if we have multiple modal layers
        const dialogs = page.locator('[role="dialog"]');
        const dialogCount = await dialogs.count();
        
        if (dialogCount > 1) {
          console.log(`✓ Multiple modal layers detected: ${dialogCount}`);
          
          // Close modals in reverse order
          for (let i = 0; i < dialogCount; i++) {
            await page.keyboard.press('Escape');
            await page.waitForTimeout(500);
          }
        } else {
          console.log('ℹ Single modal layer or no additional modal opened');
        }
      }
    });

    test('should maintain focus management in modals', async ({ page }) => {
      // Open My Account drawer
      await safeClick(page, ELEMENTS.settings.settingsButton);
      await safeClick(page, ELEMENTS.settings.myAccountOption);
      
      // Verify modal is open
      await expect(page.locator(MODAL_ELEMENTS.settings.myAccountDrawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Check if focus is trapped within the modal
      const focusableElements = page.locator('button, input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])');
      const focusableCount = await focusableElements.count();
      
      if (focusableCount > 0) {
        // Test tab navigation
        await page.keyboard.press('Tab');
        const activeElement = page.locator(':focus');
        
        if (await activeElement.count() > 0) {
          console.log('✓ Focus management working in modal');
        }
      }
      
      // Close modal
      await page.keyboard.press('Escape');
    });
  });

  test.describe('Error Modal Handling', () => {
    test('should handle error modal display and dismissal', async ({ page }) => {
      // Navigate to payments to test error modals
      await navigateToPage(page, 'payments');
      
      // Look for any existing error modals or alerts
      const errorElements = [
        '[role="alert"]',
        '.error',
        '.alert',
        'text*="error"',
        'text*="failed"',
        'text*="unable"'
      ];
      
      for (const selector of errorElements) {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          console.log(`Found ${count} potential error elements: ${selector}`);
          
          // If error element is visible, try to dismiss it
          const firstError = elements.first();
          if (await firstError.isVisible()) {
            // Look for close button within or near the error
            const closeButtons = firstError.locator('button, .close, [aria-label*="close"]');
            const closeCount = await closeButtons.count();
            
            if (closeCount > 0) {
              await closeButtons.first().click();
              await waitForPageStable(page);
              console.log('✓ Error modal dismissed successfully');
            }
          }
        }
      }
    });

    test('should verify error modal accessibility', async ({ page }) => {
      // Check for proper ARIA attributes on modal elements
      const modalElements = page.locator('[role="dialog"], [role="alert"], [role="alertdialog"]');
      const modalCount = await modalElements.count();
      
      for (let i = 0; i < modalCount; i++) {
        const modal = modalElements.nth(i);
        
        // Check for aria-label or aria-labelledby
        const ariaLabel = await modal.getAttribute('aria-label');
        const ariaLabelledBy = await modal.getAttribute('aria-labelledby');
        
        if (ariaLabel || ariaLabelledBy) {
          console.log(`✓ Modal ${i + 1} has proper ARIA labeling`);
        }
        
        // Check for aria-modal
        const ariaModal = await modal.getAttribute('aria-modal');
        if (ariaModal === 'true') {
          console.log(`✓ Modal ${i + 1} has aria-modal="true"`);
        }
      }
      
      if (modalCount === 0) {
        console.log('ℹ No modals found for accessibility testing');
      }
    });
  });

  test.describe('Session Management Modals', () => {
    test('should handle session timeout modal structure', async ({ page }) => {
      // Look for session timeout modal elements in the DOM
      const sessionElements = [
        'text="Are you still there?"',
        'text*="session"',
        'text*="timeout"',
        'text*="still there"',
        'button:has-text("Continue")',
        'button:has-text("Logout")'
      ];
      
      for (const selector of sessionElements) {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          console.log(`Found ${count} session management elements: ${selector}`);
        }
      }
      
      console.log('✓ Session management modal structure verified');
    });

    test('should verify session modal button functionality', async ({ page }) => {
      // Since we can't easily trigger a real session timeout in tests,
      // we'll verify the button elements exist and are properly structured
      
      const continueButtons = page.locator('button:has-text("Continue")');
      const logoutButtons = page.locator('button:has-text("Logout")');
      
      const continueCount = await continueButtons.count();
      const logoutCount = await logoutButtons.count();
      
      if (continueCount > 0) {
        console.log(`Found ${continueCount} Continue buttons`);
      }
      
      if (logoutCount > 0) {
        console.log(`Found ${logoutCount} Logout buttons`);
      }
      
      console.log('✓ Session modal button structure verified');
    });
  });

  test.describe('Mobile Modal and Drawer Behavior', () => {
    test('should handle mobile drawer positioning', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await waitForPageStable(page);
      
      // Open My Account drawer on mobile
      await safeClick(page, ELEMENTS.settings.settingsButton);
      await safeClick(page, ELEMENTS.settings.myAccountOption);
      
      // Verify drawer opened
      await expect(page.locator(MODAL_ELEMENTS.settings.myAccountDrawer.title)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // On mobile, drawers often appear from bottom
      const drawer = page.locator('[role="dialog"]').first();
      if (await drawer.isVisible()) {
        const boundingBox = await drawer.boundingBox();
        if (boundingBox) {
          console.log(`✓ Mobile drawer positioned at: ${boundingBox.x}, ${boundingBox.y}`);
        }
      }
      
      // Close drawer
      await page.keyboard.press('Escape');
      console.log('✓ Mobile drawer functionality verified');
    });

    test('should handle mobile modal touch interactions', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await waitForPageStable(page);
      
      // Open card management drawer
      await page.locator(ELEMENTS.dashboard.myCardsButton).tap();
      await waitForPageStable(page);
      
      // Verify drawer opened
      if (await page.locator(MODAL_ELEMENTS.cardManagement.drawer.title).isVisible({ timeout: 3000 })) {
        console.log('✓ Mobile touch interaction opened drawer');
        
        // Test touch close
        const closeButton = page.locator(MODAL_ELEMENTS.cardManagement.drawer.closeButton);
        if (await closeButton.isVisible({ timeout: 2000 })) {
          await closeButton.tap();
          await waitForPageStable(page);
          console.log('✓ Mobile touch close functionality verified');
        }
      }
    });

    test('should verify mobile modal viewport coverage', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await waitForPageStable(page);
      
      // Open a modal
      await safeClick(page, ELEMENTS.settings.settingsButton);
      await safeClick(page, ELEMENTS.settings.myAccountOption);
      
      // Verify modal covers appropriate viewport area
      const modal = page.locator('[role="dialog"]').first();
      if (await modal.isVisible({ timeout: 3000 })) {
        const boundingBox = await modal.boundingBox();
        const viewport = page.viewportSize();
        
        if (boundingBox && viewport) {
          const coverageRatio = (boundingBox.width * boundingBox.height) / (viewport.width * viewport.height);
          console.log(`✓ Mobile modal viewport coverage: ${(coverageRatio * 100).toFixed(1)}%`);
        }
      }
      
      await page.keyboard.press('Escape');
    });
  });
});
