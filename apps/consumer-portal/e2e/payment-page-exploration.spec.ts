import { test, expect } from '@playwright/test';
import { loginToPortal, VIEWPORTS, navigateToPage, waitForPageStable, TIMEOUTS } from './test-utils';

test.describe('Payment Page Exploration', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(VIEWPORTS.desktop);
    await loginToPortal(page);
  });

  test('should explore payments page elements', async ({ page }) => {
    // Navigate to payments page
    await navigateToPage(page, 'payments');
    await waitForPageStable(page, TIMEOUTS.medium);

    // Take a screenshot to see what's on the page
    await page.screenshot({ path: 'test-results/payments-page-exploration.png', fullPage: true });

    // Try to find and click My Linked Accounts
    console.log('Looking for My Linked Accounts...');

    // Try different selectors
    const selectors = [
      '*:has-text("My Linked Accounts")',
      '.fa-credit-card',
      'button:has(.fa-credit-card)',
      'div:has-text("My Linked Accounts")',
      'span:has-text("My Linked Accounts")',
      '[class*="fa-credit-card"]'
    ];

    for (const selector of selectors) {
      try {
        const elements = await page.locator(selector).all();
        console.log(`Selector "${selector}" found ${elements.length} elements`);

        for (let i = 0; i < elements.length; i++) {
          const element = elements[i];
          const isVisible = await element.isVisible();
          const text = await element.textContent();
          const tagName = await element.evaluate(el => el.tagName);
          console.log(`  Element ${i}: ${tagName} - visible: ${isVisible} - text: "${text?.trim()}"`);

          if (isVisible && (text?.includes('My Linked Accounts') || selector.includes('fa-credit-card'))) {
            console.log(`  Attempting to click element ${i}...`);
            try {
              await element.click();
              await page.waitForTimeout(2000);
              await page.screenshot({ path: `test-results/after-click-${i}.png`, fullPage: true });

              // Check if drawer opened
              const drawer = page.locator('[role="dialog"], .MuiDrawer-root');
              const drawerVisible = await drawer.isVisible();
              console.log(`  Drawer visible after click: ${drawerVisible}`);

              if (drawerVisible) {
                const drawerContent = await drawer.textContent();
                console.log(`  Drawer content: "${drawerContent?.trim()}"`);
                return; // Success!
              }
            } catch (clickError) {
              console.log(`  Click failed: ${clickError}`);
            }
          }
        }
      } catch (error) {
        console.log(`Selector "${selector}" failed: ${error}`);
      }
    }

    console.log('Could not find or click My Linked Accounts element');
  });
});
