import { test, expect } from '@playwright/test';
import {
  loginToPortal,
  navigateToPage,
  switchToTab,
  ensureNavigationAccessible,
  isMobileNavigation,
  accessMobileNavigation,
  verifyNavigation,
  safeClick,
  waitForPageStable,
  ELEMENTS,
  TIMEOUTS,
  VIEWPORTS
} from './test-utils';

test.describe('Navigation & Core Functionality Tests', () => {
  test.beforeEach(async ({ page }) => {
    await loginToPortal(page);
    await waitForPageStable(page);
  });

  test.describe('Primary Navigation Flow', () => {
    test('should navigate through all main pages in circular flow', async ({ page }) => {
      console.log('Testing circular navigation: Home → Payments → Rewards → Home');

      // Start at home
      await navigateToPage(page, 'home');
      await expect(page).toHaveURL(/\/$/);
      
      // Navigate to Payments
      await navigateToPage(page, 'payments');
      await expect(page).toHaveURL(/\/payments$/);
      await expect(page.locator('text="Payments"')).toBeVisible();

      // Navigate to Rewards
      await navigateToPage(page, 'rewards');
      await expect(page).toHaveURL(/\/rewards$/);
      await expect(page.locator('text="Rewards"')).toBeVisible();

      // Navigate back to Home
      await navigateToPage(page, 'home');
      await expect(page).toHaveURL(/\/$/);
      await expect(page.locator(ELEMENTS.dashboard.welcomeBanner)).toBeVisible();

      console.log('✓ Circular navigation completed successfully');
    });

    test('should verify navigation links have correct hrefs', async ({ page }) => {
      await navigateToPage(page, 'home');
      await verifyNavigation(page);
      console.log('✓ All navigation links verified');
    });

    test('should handle direct URL navigation', async ({ page }) => {
      // Test direct navigation to each page
      const pages = [
        { url: '/', expectedContent: 'Current Balance' },
        { url: '/payments', expectedContent: 'Payments' },
        { url: '/rewards', expectedContent: 'Rewards' }
      ];

      for (const pageInfo of pages) {
        await page.goto(pageInfo.url);
        await waitForPageStable(page);
        await expect(page.locator(`text="${pageInfo.expectedContent}"`)).toBeVisible({ timeout: TIMEOUTS.medium });
        console.log(`✓ Direct navigation to ${pageInfo.url} successful`);
      }
    });
  });

  test.describe('Tab Switching Functionality', () => {
    test('should switch between Recent Activity and Statements tabs', async ({ page }) => {
      await navigateToPage(page, 'home');

      // Verify both tabs are visible
      const recentActivityTab = page.locator(ELEMENTS.tabs.recentActivity).first();
      const statementsTab = page.locator(ELEMENTS.tabs.statements).first();
      
      await expect(recentActivityTab).toBeVisible({ timeout: TIMEOUTS.medium });
      await expect(statementsTab).toBeVisible({ timeout: TIMEOUTS.medium });

      // Test switching to Statements tab
      await switchToTab(page, 'statements');
      await expect(statementsTab).toHaveAttribute('aria-selected', 'true');
      console.log('✓ Switched to Statements tab');

      // Test switching back to Recent Activity tab
      await switchToTab(page, 'recentActivity');
      await expect(recentActivityTab).toHaveAttribute('aria-selected', 'true');
      console.log('✓ Switched back to Recent Activity tab');
    });

    test('should maintain tab state during navigation', async ({ page }) => {
      await navigateToPage(page, 'home');
      
      // Switch to Statements tab
      await switchToTab(page, 'statements');
      
      // Navigate away and back
      await navigateToPage(page, 'payments');
      await navigateToPage(page, 'home');
      
      // Verify Recent Activity tab is selected (default state)
      const recentActivityTab = page.locator(ELEMENTS.tabs.recentActivity).first();
      await expect(recentActivityTab).toHaveAttribute('aria-selected', 'true');
      console.log('✓ Tab state reset correctly after navigation');
    });
  });

  test.describe('Mobile Navigation', () => {
    test('should handle mobile hamburger menu navigation', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize(VIEWPORTS.mobile);
      await navigateToPage(page, 'home');

      // Check if we're in mobile mode
      const isMobile = await isMobileNavigation(page);
      
      if (isMobile) {
        console.log('Mobile navigation detected, testing hamburger menu...');
        
        // Test hamburger menu functionality
        await accessMobileNavigation(page);
        
        // Verify navigation items are visible after opening menu
        const homeNav = page.locator(ELEMENTS.navigation.home).first();
        const paymentsNav = page.locator(ELEMENTS.navigation.payments).first();
        const rewardsNav = page.locator(ELEMENTS.navigation.rewards).first();
        
        await expect(homeNav).toBeVisible({ timeout: TIMEOUTS.medium });
        await expect(paymentsNav).toBeVisible({ timeout: TIMEOUTS.medium });
        await expect(rewardsNav).toBeVisible({ timeout: TIMEOUTS.medium });
        
        // Test navigation through mobile menu
        await safeClick(page, ELEMENTS.navigation.payments);
        await expect(page).toHaveURL(/\/payments$/);
        
        console.log('✓ Mobile navigation working correctly');
      } else {
        console.log('Desktop navigation detected in mobile viewport - this is acceptable');
      }
    });

    test('should toggle hamburger menu open/close', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await navigateToPage(page, 'home');

      const isMobile = await isMobileNavigation(page);
      
      if (isMobile) {
        const hamburgerButton = page.locator(ELEMENTS.mobile.hamburgerMenu).first();
        
        // Open menu
        await hamburgerButton.click();
        await page.waitForTimeout(800); // Wait for animation
        
        // Verify menu opened (navigation items should be visible)
        const homeNav = page.locator(ELEMENTS.navigation.home).first();
        await expect(homeNav).toBeVisible({ timeout: TIMEOUTS.short });
        
        // Close menu by clicking hamburger again
        await hamburgerButton.click();
        await page.waitForTimeout(800); // Wait for animation
        
        console.log('✓ Hamburger menu toggle functionality verified');
      }
    });
  });

  test.describe('Settings Menu', () => {
    test('should open and close settings menu', async ({ page }) => {
      await navigateToPage(page, 'home');

      // Click settings button
      const settingsButton = page.locator(ELEMENTS.settings.settingsButton).first();
      await expect(settingsButton).toBeVisible({ timeout: TIMEOUTS.medium });
      await settingsButton.click();

      // Verify menu options are visible
      await expect(page.locator(ELEMENTS.settings.myAccountOption)).toBeVisible({ timeout: TIMEOUTS.short });
      await expect(page.locator(ELEMENTS.settings.documentsOption)).toBeVisible({ timeout: TIMEOUTS.short });
      await expect(page.locator(ELEMENTS.settings.logoutOption)).toBeVisible({ timeout: TIMEOUTS.short });

      // Close menu by clicking elsewhere
      await page.locator('body').click();
      await page.waitForTimeout(500);

      console.log('✓ Settings menu functionality verified');
    });

    test('should open My Account drawer', async ({ page }) => {
      await navigateToPage(page, 'home');

      // Open settings menu and click My Account
      await safeClick(page, ELEMENTS.settings.settingsButton);
      await safeClick(page, ELEMENTS.settings.myAccountOption);

      // Verify My Account drawer opened
      await expect(page.locator('text="My Account"')).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Close drawer
      const closeButton = page.locator('button[aria-label="Close"]').first();
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }

      console.log('✓ My Account drawer functionality verified');
    });

    test('should open Documents drawer', async ({ page }) => {
      await navigateToPage(page, 'home');

      // Open settings menu and click Documents
      await safeClick(page, ELEMENTS.settings.settingsButton);
      await safeClick(page, ELEMENTS.settings.documentsOption);

      // Verify Documents drawer opened
      await expect(page.locator('text="Documents"')).toBeVisible({ timeout: TIMEOUTS.medium });
      
      // Close drawer
      const closeButton = page.locator('button[aria-label="Close"]').first();
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }

      console.log('✓ Documents drawer functionality verified');
    });
  });

  test.describe('Navigation Error Handling', () => {
    test('should handle navigation during loading states', async ({ page }) => {
      // Navigate quickly between pages to test loading states
      await navigateToPage(page, 'home');
      await navigateToPage(page, 'payments');
      await navigateToPage(page, 'rewards');
      await navigateToPage(page, 'home');

      // Verify final state is correct
      await expect(page).toHaveURL(/\/$/);
      await expect(page.locator(ELEMENTS.dashboard.welcomeBanner)).toBeVisible({ timeout: TIMEOUTS.medium });
      
      console.log('✓ Rapid navigation handled correctly');
    });

    test('should maintain navigation accessibility across viewport changes', async ({ page }) => {
      // Start with desktop
      await page.setViewportSize(VIEWPORTS.desktop);
      await navigateToPage(page, 'home');
      await verifyNavigation(page);

      // Switch to mobile
      await page.setViewportSize(VIEWPORTS.mobile);
      await waitForPageStable(page);
      await ensureNavigationAccessible(page);
      
      // Verify navigation still works
      await navigateToPage(page, 'payments');
      await expect(page).toHaveURL(/\/payments$/);

      console.log('✓ Navigation accessibility maintained across viewport changes');
    });
  });
});
