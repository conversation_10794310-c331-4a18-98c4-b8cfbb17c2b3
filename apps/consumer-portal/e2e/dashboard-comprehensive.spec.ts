import { test, expect } from '@playwright/test';
import {
  loginToPortal,
  navigateToPage,
  switchToTab,
  verifyDashboardContent,
  safeClick,
  waitForPageStable,
  findStatementPdfs,
  verifyPdfLink,
  downloadAndVerifyPdfStructure,
  ELEMENTS,
  TIMEOUTS,
  VIEWPORTS
} from './test-utils';

test.describe('Dashboard Interaction Tests', () => {
  test.beforeEach(async ({ page }) => {
    await loginToPortal(page);
    await navigateToPage(page, 'home');
    await waitForPageStable(page);
  });

  test.describe('Dashboard Content Verification', () => {
    test('should display all required dashboard elements', async ({ page }) => {
      console.log('Verifying dashboard content...');

      await verifyDashboardContent(page);

      // Verify welcome banner with user name
      await expect(page.locator(ELEMENTS.dashboard.welcomeBanner)).toBeVisible({ timeout: TIMEOUTS.medium });

      // Verify balance displays
      await expect(page.locator(ELEMENTS.dashboard.currentBalance).first()).toBeVisible({ timeout: TIMEOUTS.medium });
      await expect(page.locator(ELEMENTS.dashboard.availableCredit).first()).toBeVisible({ timeout: TIMEOUTS.medium });
      await expect(page.locator(ELEMENTS.dashboard.rewardsBalance).first()).toBeVisible({ timeout: TIMEOUTS.medium });
      await expect(page.locator(ELEMENTS.dashboard.statementBalance).first()).toBeVisible({ timeout: TIMEOUTS.medium });

      // Verify autopay status is displayed
      await expect(page.locator(ELEMENTS.dashboard.autopayStatus)).toBeVisible({ timeout: TIMEOUTS.medium });

      console.log('✓ All dashboard elements verified');
    });

    test('should display balance amounts correctly', async ({ page }) => {
      // Verify balance amounts are displayed as currency
      const balanceElements = [
        ELEMENTS.dashboard.currentBalance,
        ELEMENTS.dashboard.availableCredit,
        ELEMENTS.dashboard.statementBalance
      ];

      for (const selector of balanceElements) {
        const element = page.locator(selector);
        await expect(element).toBeVisible({ timeout: TIMEOUTS.medium });

        // Look for currency amounts near the balance labels
        const parentElement = element.locator('..');
        const currencyPattern = /\$[\d,]+\.?\d*/;
        const parentText = await parentElement.textContent();

        if (parentText) {
          expect(parentText).toMatch(currencyPattern);
        }
      }

      console.log('✓ Balance amounts displayed correctly');
    });

    test('should show rewards balance with numeric value', async ({ page }) => {
      const rewardsElement = page.locator(ELEMENTS.dashboard.rewardsBalance);
      await expect(rewardsElement).toBeVisible({ timeout: TIMEOUTS.medium });

      // Look for numeric rewards value
      const parentElement = rewardsElement.locator('..');
      const numericPattern = /[\d,]+/;
      const parentText = await parentElement.textContent();

      if (parentText) {
        expect(parentText).toMatch(numericPattern);
      }

      console.log('✓ Rewards balance displayed correctly');
    });
  });

  test.describe('Action Button Testing', () => {
    test('should navigate to payments page via Make a Payment button', async ({ page }) => {
      const makePaymentButton = page.locator(ELEMENTS.dashboard.makePaymentButton).first();
      await expect(makePaymentButton).toBeVisible({ timeout: TIMEOUTS.medium });
      await expect(makePaymentButton).toBeEnabled({ timeout: TIMEOUTS.short });

      // Verify href attribute
      const href = await makePaymentButton.getAttribute('href');
      expect(href).toBe('/payments');

      // Click and verify navigation
      await makePaymentButton.click();
      await waitForPageStable(page);
      await expect(page).toHaveURL(/\/payments$/);

      console.log('✓ Make a Payment button navigation verified');
    });

    test('should navigate to rewards page via Redeem button', async ({ page }) => {
      const redeemButton = page.locator(ELEMENTS.dashboard.redeemButton).first();
      await expect(redeemButton).toBeVisible({ timeout: TIMEOUTS.medium });
      await expect(redeemButton).toBeEnabled({ timeout: TIMEOUTS.short });

      // Verify href attribute
      const href = await redeemButton.getAttribute('href');
      expect(href).toBe('/rewards');

      // Click and verify navigation
      await redeemButton.click();
      await waitForPageStable(page);
      await expect(page).toHaveURL(/\/rewards$/);

      console.log('✓ Redeem button navigation verified');
    });

    test('should open card management drawer via My Cards button', async ({ page }) => {
      const myCardsButton = page.locator(ELEMENTS.dashboard.myCardsButton).first();
      await expect(myCardsButton).toBeVisible({ timeout: TIMEOUTS.medium });
      await expect(myCardsButton).toBeEnabled({ timeout: TIMEOUTS.short });

      // Click to open drawer
      await myCardsButton.click();
      await waitForPageStable(page);

      // Verify drawer opened by looking for "My Cards" title in drawer
      await expect(page.locator('text="My Cards"')).toBeVisible({ timeout: TIMEOUTS.medium });

      // Close drawer if close button is available
      const closeButton = page.locator('button[aria-label="Close"]').first();
      if (await closeButton.isVisible({ timeout: 2000 })) {
        await closeButton.click();
        await waitForPageStable(page);
      }

      console.log('✓ My Cards button functionality verified');
    });

    test('should verify autopay setup link functionality', async ({ page }) => {
      // Look for autopay setup link (if autopay is OFF)
      const autopaySetupLink = page.locator('a:has-text("set up")');

      if (await autopaySetupLink.isVisible({ timeout: 3000 })) {
        const href = await autopaySetupLink.getAttribute('href');
        expect(href).toMatch(/\/payments\?q=autopay/);

        // Click and verify navigation
        await autopaySetupLink.click();
        await waitForPageStable(page);
        await expect(page).toHaveURL(/\/payments\?q=autopay/);

        console.log('✓ Autopay setup link verified');
      } else {
        console.log('ℹ Autopay setup link not visible (autopay may already be enabled)');
      }
    });
  });

  test.describe('Transaction Interaction Testing', () => {
    test('should display transaction list in Recent Activity tab', async ({ page }) => {
      // Ensure we're on Recent Activity tab
      await switchToTab(page, 'recentActivity');

      // Look for transaction elements
      const transactionSelectors = [
        'text="Pending"',
        'text="Posted"',
        '[role="button"]', // Transaction buttons
        'text=/\$[\d,]+\.?\d*/' // Currency amounts
      ];

      let transactionsFound = false;
      for (const selector of transactionSelectors) {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          transactionsFound = true;
          console.log(`Found ${count} elements matching: ${selector}`);
        }
      }

      if (transactionsFound) {
        console.log('✓ Transaction elements found in Recent Activity tab');
      } else {
        console.log('ℹ No transactions found (account may be new)');
      }
    });

    test('should handle clicking individual transactions', async ({ page }) => {
      await switchToTab(page, 'recentActivity');

      // Look for clickable transaction elements
      const transactionButtons = page.locator('[role="button"]').filter({
        hasText: /\$/ // Filter for elements containing currency
      });

      const count = await transactionButtons.count();

      if (count > 0) {
        const firstTransaction = transactionButtons.first();
        await expect(firstTransaction).toBeVisible({ timeout: TIMEOUTS.medium });

        // Click the transaction (this might open a detail view or modal)
        await firstTransaction.click();
        await waitForPageStable(page);

        console.log('✓ Transaction click handled successfully');
      } else {
        console.log('ℹ No clickable transactions found');
      }
    });
  });

  test.describe('Statement PDF Access Testing', () => {
    test('should display statement PDFs in Statements tab', async ({ page }) => {
      await switchToTab(page, 'statements');

      // Look for PDF links
      const pdfFound = await verifyPdfLink(page, 'a[href*=".pdf"]');

      if (pdfFound) {
        console.log('✓ Statement PDF links found and verified');
      } else {
        console.log('ℹ No statement PDFs found (account may be new)');
      }
    });

    test('should verify PDF link accessibility and structure', async ({ page }) => {
      await switchToTab(page, 'statements');

      const statementPdf = await findStatementPdfs(page);

      if (statementPdf) {
        // Verify PDF link properties
        await expect(statementPdf).toBeVisible({ timeout: TIMEOUTS.medium });
        await expect(statementPdf).toBeEnabled({ timeout: TIMEOUTS.short });

        const href = await statementPdf.getAttribute('href');
        expect(href).toBeTruthy();
        expect(href).toContain('.pdf');

        const linkText = await statementPdf.textContent();
        expect(linkText).toBeTruthy();

        console.log(`✓ PDF link verified: "${linkText?.trim()}" -> ${href}`);

        // Test PDF structure verification
        const pdfVerified = await downloadAndVerifyPdfStructure(page);
        if (pdfVerified) {
          console.log('✓ PDF structure verification completed');
        }
      } else {
        console.log('ℹ No statement PDFs available for testing');
      }
    });

    test('should handle PDF opening behavior', async ({ page }) => {
      await switchToTab(page, 'statements');

      const statementPdf = await findStatementPdfs(page);

      if (statementPdf) {
        // Test PDF click behavior (may open in new tab or download)
        const [newPage] = await Promise.all([
          page.context().waitForEvent('page', { timeout: TIMEOUTS.medium }).catch(() => null),
          statementPdf.click()
        ]);

        if (newPage) {
          // PDF opened in new tab
          await newPage.waitForLoadState('load', { timeout: TIMEOUTS.medium });
          const newPageUrl = newPage.url();
          expect(newPageUrl).toContain('.pdf');
          await newPage.close();
          console.log('✓ PDF opened in new tab successfully');
        } else {
          // PDF likely downloaded or opened inline
          console.log('✓ PDF click handled (download or inline view)');
        }
      }
    });
  });

  test.describe('Responsive Dashboard Testing', () => {
    test('should maintain functionality on mobile viewport', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await waitForPageStable(page);

      // Verify key elements are still visible and functional
      await verifyDashboardContent(page);

      // Test action buttons on mobile
      const makePaymentButton = page.locator(ELEMENTS.dashboard.makePaymentButton).first();
      if (await makePaymentButton.isVisible({ timeout: 3000 })) {
        await expect(makePaymentButton).toBeEnabled();
      }

      const redeemButton = page.locator(ELEMENTS.dashboard.redeemButton).first();
      if (await redeemButton.isVisible({ timeout: 3000 })) {
        await expect(redeemButton).toBeEnabled();
      }

      console.log('✓ Dashboard functionality maintained on mobile');
    });

    test('should handle tab switching on mobile', async ({ page }) => {
      await page.setViewportSize(VIEWPORTS.mobile);
      await waitForPageStable(page);

      // Test tab switching on mobile
      await switchToTab(page, 'statements');
      const statementsTab = page.locator(ELEMENTS.tabs.statements).first();
      await expect(statementsTab).toHaveAttribute('aria-selected', 'true');

      await switchToTab(page, 'recentActivity');
      const recentActivityTab = page.locator(ELEMENTS.tabs.recentActivity).first();
      await expect(recentActivityTab).toHaveAttribute('aria-selected', 'true');

      console.log('✓ Tab switching works correctly on mobile');
    });
  });
});
